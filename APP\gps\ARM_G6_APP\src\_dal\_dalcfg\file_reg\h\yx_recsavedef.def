#if EN_FD_AEB > 0
//#include "yx_fd_aebs.h"
#endif
#if EN_AEB > 0
//#include "yx_ft_aebs.h"
#endif

#if EN_RUNRECORD > 0
RECSAVE_DEF(RECSAVE_TYPE_SPEED, "SPEED", sizeof(TR_SPEED_DATA_T), MAXNUM_OF_SPEED,  PERIOD_SPEED, YX_TR12_InformSpeedDelete)
RECSAVE_DEF(RECSAVE_TYPE_TRAIL, "TRAIL", sizeof(TR_TRAIL_DATA_T), MAXNUM_OF_TRAIL,  PERIOD_TRAIL, YX_TR12_InformTrailDelete)
#endif

#if EN_RESEND > 0
RECSAVE_DEF(RECSAVE_TYPE_POSMSG, "RESND",  sizeof(RESEND_STORE_T),    MAX_POSREC_1, STORE_PERIOD, YX_Com1InformPosRecDel)
RECSAVE_DEF(RECSAVE_TYPE_POSMSG2, "RES2",  sizeof(RESEND_STORE_T),    MAX_POSREC_2, STORE_PERIOD, YX_Com2InformPosRecDel)
RECSAVE_DEF(RECSAVE_TYPE_POSMSG3, "RES3",  sizeof(RESEND_STORE_T),    MAX_POSREC_3, STORE_PERIOD, YX_Com3InformPosRecDel)
#endif

#if (EN_120R)
RECSAVE_DEF(RECSAVE_TYPE_ASK,     "ASK",    sizeof(ASK_T),            1,   1, NULL)
RECSAVE_DEF(RECSAVE_TYPE_INFOS,   "INFOS",  sizeof(INFOSERVER_T),     MAX_INFOSERVER,   1, NULL)
RECSAVE_DEF(RECSAVE_TYPE_AROUND,  "AROUN",  sizeof(AROUNDSERVER_T),   MAX_AROUND,       1, NULL)
#elif EN_120ND70
RECSAVE_DEF(RECSAVE_TYPE_ASK,     "ASK",    sizeof(ND70_ASK_T),            1,   1, NULL)
RECSAVE_DEF(RECSAVE_TYPE_INFOS,   "INFOS",  sizeof(ND70_INFOSERVER_T),     MAX_INFOSERVER,   1, NULL)
RECSAVE_DEF(RECSAVE_TYPE_AROUND,  "AROUN",  sizeof(ND70_AROUNDSERVER_T),   MAX_AROUND,       1, NULL)
#endif

#if EN_GNSS > 0
RECSAVE_DEF(RECSAVE_TYPE_NMEA,    "NMEA",   sizeof(NMEA_REC_T),       MAX_NMEAREC, 10, YX_InformNMEARecDel)
#endif  
RECSAVE_DEF(RECSAVE_TYPE_CAN1, "CANL1",    sizeof(CAN_DATA_T),  MAX_NUM_CANDATA, STORE_PERIOD, YX_Informcan1recdel)
RECSAVE_DEF(RECSAVE_TYPE_CAN2, "CANL2",    sizeof(CAN_DATA_T),  MAX_NUM_CANDATA, STORE_PERIOD, YX_Informcan2recdel)

//RECSAVE_DEF(RECSAVE_TYPE_SPN,  "SPN",     sizeof(SPN_DATA_T),  MAX_SPNREC,      STORE_PERIOD, NULL)
////RECSAVE_DEF(RECSAVE_TYPE_SPNO,  "SPNO",     sizeof(SPN_DATA_ORG_T),  MAX_SPNREC,      1, NULL)
#if EN_FTPRO_V3 > 0
RECSAVE_DEF(RECSAVE_TYPE_FTPRO, "FTPRO",  sizeof(FTPRO_DATA_T),   MAX_PRO_FTPRO_REC, STORE_PERIOD, YX_PRO_DelResendCH1)
RECSAVE_DEF(RECSAVE_TYPE_SPN,   "SPN",    sizeof(FT_SPNDATA_T),   MAX_SPN_VCH, 3, NULL)
#endif

#if EN_FTZK > 0
RECSAVE_DEF(RECSAVE_TYPE_LOCK,  "LOCK",    sizeof(FTOTR_DATA_T),   MAX_LC_LCREC, 1, YX_LC_DelResendCH1)
RECSAVE_DEF(RECSAVE_TYPE_ECUST, "ECUST",   sizeof(FTOTR_DATA_T),   MAX_LC_STREC, STORE_PERIOD, YX_LC_DelResendCH2)
#endif
#if EN_OBD > 0
RECSAVE_DEF(RECSAVE_TYPE_OBD1, "OBD1",  sizeof(OBD_RESEND_STORE_T), MAX_OBD_NER1_REC, STORE_PERIOD, YX_OBD_DelResend1)
RECSAVE_DEF(RECSAVE_TYPE_OBD2, "OBD2",  sizeof(OBD_RESEND_STORE_T), MAX_OBD_NER2_REC, 1, YX_OBD_DelResend2)
#endif

#if (EN_FD_AEB || EN_FD_AEB_LKA || EN_AEB)

	#define MAX_RECORED_AEB_LEN		650//MAX_FD_AEB_ONE_RECORD_LEN
	#define MAX_RECORED_AEB_NUM		6000//MAX_FD_AEB_RECORD_NUM

RECSAVE_DEF(RECSAVE_TYPE_AEB, "AEB", MAX_RECORED_AEB_LEN, MAX_RECORED_AEB_NUM, STORE_PERIOD, YX_PRO_DelAEBResend)
#endif

#if EN_CAN_ALLCLOOECT > 0
RECSAVE_DEF(RECSAVE_TYPE_CLCAN, "CLCAN",  sizeof(FT_RESULTFILE_T),   MAX_FILENUM , STORE_PERIOD, NULL)
#endif

