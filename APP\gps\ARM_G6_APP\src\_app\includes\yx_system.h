/****************************************************************
**                                                              *
**  FILE         :  SW_System.h                                  *
**  COPYRIGHT    :  (c) 2004 .Xiamen Yaxon NetWork CO.LTD       *
**                                                              *
**                                                              *
**              2004/10/22                                      *
****************************************************************/

#ifndef DEF_SYSTEM
#define DEF_SYSTEM

/* some useful macros */
#define OFFSET(structure, member)	            /* byte offset of member in structure*/\
		((int) &(((structure *) 0) -> member))

#define MEMBER_SIZE(structure, member)	        /* size of a member of a structure */\
		(sizeof (((structure *) 0) -> member))

#define NELEMENTS(array)		                /* number of elements in an array */ \
		(sizeof (array) / sizeof ((array) [0]))

#define FOREVER	for (;;)

/****************************************************************
    DEFINE THE DATATYPE
****************************************************************/
#ifndef BYTE_FORMAT_DEFINE
	#define BYTE_FORMAT_DEFINE
	#define GCC_PACKED __attribute__((packed))
	#define GCC_ALIGN0 __attribute__((aligned(1)))
#endif


typedef unsigned char  BOOLEAN;
typedef unsigned char  INT8U;                   /* Unsigned  8 bit quantity                           */
typedef signed   char  INT8S;                   /* Signed    8 bit quantity                           */
typedef unsigned short INT16U;                  /* Signed   16 bit quantity                           */
typedef signed   short INT16S;                  /* Unsigned 32 bit quantity                           */
typedef unsigned int   INT32U;                  /* Unsigned 32 bit quantity                           */
typedef unsigned long  long INT64U;             /* Unsigned 64 bit quantity                           */
typedef signed   long  long INT64S;             /* signed 64 bit quantity                           */
typedef signed   int   INT32S;                  /* Signed   32 bit quantity                           */
typedef float          FP32;                    /* Single precision floating point                    */
typedef double         FP64;                    /* Double precision floating point                    */
typedef  unsigned long ip_addr;

#ifndef U8
typedef unsigned char  U8;
#endif
#ifndef U16
typedef unsigned short  U16;
#endif
#ifndef U32
typedef unsigned int  U32;
#endif
#ifndef U64
typedef unsigned long  long  U64;
#endif
#ifndef S8
typedef signed char  S8;
#endif
#ifndef S16
typedef signed short  S16;
#endif
#ifndef S32
typedef signed int  S32;
#endif
#ifndef S64
typedef signed long  long  S64;
#endif
#ifndef BOOL
typedef signed int  BOOL;
#endif


/****************************************************************
    DEFINE SOME SWITCH
****************************************************************/

#ifndef  OFF
#define  OFF                    0
#endif

#ifndef  ON
#define  ON                     1
#endif

#ifndef  false
#define  false                  0
#endif

#ifndef  true
#define  true                   1
#endif

#ifndef  TRUE
#define  TRUE                   1
#endif

#ifndef   FALSE
#define   FALSE                 0
#endif


#ifndef   NULL 
#define   NULL                  ((void*)0)
#endif

#ifndef  PNULL
#define  PNULL                  ((void *)0)
#endif

#ifndef	 CR
#define  CR                      0x0D
#endif

#ifndef  LF
#define  LF                      0x0A
#endif

#ifndef	 CTRL_Z
#define  CTRL_Z                  0x1A
#endif

#ifndef  ESC
#define  ESC                     0x1B
#endif

#ifndef	 _SUCCESS
#define  _SUCCESS                0
#endif

#ifndef	 _FAILURE
#define  _FAILURE                1
#endif

#ifndef  _OVERTIME
#define  _OVERTIME               2
#endif

#ifndef  OPEN
#define  OPEN                    0xAA
#endif

#ifndef  CLOSE
#define  CLOSE                   0x55
#endif

#endif /* DEF_SYSTEM */
