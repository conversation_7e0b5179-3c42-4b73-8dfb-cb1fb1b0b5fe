#ifndef _IOTSECUSRV_SYMM_HEADER_H_
#define _IOTSECUSRV_SYMM_HEADER_H_

#include "iotsecusrv_def.h"

#ifdef __cplusplus
extern "C" {
#endif

	/*
	* brief symmetry gen key
	*
	* @param type symmetry type ,see struct symm_key_type_st
	* @param buf calc symmmtry key buf
	* @param buf_len length for buf
	* @param datatype src encoded style
	* @param key symmetry key buf encoded with hex
	* @param len symmetry key buf len
	*
	* @return 0 - success ,other - failed
	*
	*/
	int szitrus_symm_genkey(
		const SYMM_KEY_TYPE type,
		const char* buf,
		const int buf_len,
		const char* datatype,
		char** key,
		int* len
	);

	/*
	* brief symmetry encrypt buffer with key
	*
	* @param src the buf to be encrypt
	* @param src_len the len for src
	* @param datatype src encoded style
	* @param key the key to encrypt encoded with hex
	* @param key_len the len for key
	* @param iv the iv for cbc mode ,only cbc use
	* @param type symmetry type ,see struct symm_key_type_st
	* @param mode the symmetry encrypt mode
	* @param encrypt the buffer to recv encrypted buffer encoded with base64
	* @param encrypt_len the len for encrypted buf
	*
	* @return 0 - success ,other - failed
	*
	*/

	int szitrus_symm_encrypt(
		const char* src,
		const int src_len,
		const char* datatype,
		const char* key,
		const int key_len,
		const char* iv,
		const SYMM_KEY_TYPE type,
		const SYMM_MODE mode,
		char** encrypt,
		int* encrypt_len
	);

	/*
	* brief symmetry decrypt buffer with key
	*
	* @param encrypted the buf to be decrypt encoded with base64
	* @param encrypted_len the len for encrypted
	* @param key the key to encrypt encoded with hex
	* @param key the key to encrypt
	* @param key_len the len for key
	* @param iv the iv for cbc mode ,only cbc use
	* @param type symmetry type ,see struct symm_key_type_st
	* @param mode the symmetry encrypt mode
	* @param datatype decrypt encoded style
	* @param decrypt the buffer to recv decrypted buffer encoded with datatype
	* @param decrypt_len the len for decrypted buf
	*
	* @return 0 - success ,other - failed
	*
	*/

	int szitrus_symm_decrypt(
		const char* encrypted,
		const int encrypted_len,
		const char* key,
		const int key_len,
		const char* iv,
		const SYMM_KEY_TYPE type,
		const SYMM_MODE mode,
		const char* datatype,
		char** decrypt,
		int* decrypt_len
	);

	/*
	* brief symmetry encrypt buffer with key
	*
	* @param path the file to be encrypt
	* @param key the key to encrypt encoded with hex
	* @param key_len the len for key
	* @param iv the iv for cbc mode ,only cbc use
	* @param type symmetry type ,see struct symm_key_type_st
	* @param mode the symmetry encrypt mode
	* @param des_path the file to write encrypted buffer
	*
	* @return 0 - success ,other - failed
	*
	*/

	int szitrus_symm_encrypt_file(
		const char* path,
		const char* key,
		const int key_len,
		const char* iv,
		const SYMM_KEY_TYPE type,
		const SYMM_MODE mode,
		const char* des_path
	);

	/*
	* brief symmetry decrypt buffer with key
	*
	* @param path the file to be decrypt
	* @param key the key to encrypt encoded with hex
	* @param key the key to encrypt
	* @param key_len the len for key
	* @param iv the iv for cbc mode ,only cbc use
	* @param type symmetry type ,see struct symm_key_type_st
	* @param mode the symmetry encrypt mode
	* @param des_path the file to write decrypted buffer
	*
	* @return 0 - success ,other - failed
	*
	*/

	int szitrus_symm_decrypt_file(
		const char* path,
		const char* key,
		const int key_len,
		const char* iv,
		const SYMM_KEY_TYPE type,
		const SYMM_MODE mode,
		const char* des_path
	);


	/*
	* brief symmetry algroithm rate of progress
	*
	*
	* @return 0 - success(symm over) ,other - symm algroithm running,rate of progress
	*
	*/

	int szitrus_symm_progress();

#ifdef __cplusplus
}
#endif


#endif


