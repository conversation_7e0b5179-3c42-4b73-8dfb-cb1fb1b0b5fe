/****************************************************************
**                                                              *
**  FILE         :  INCLUDES.H                                    *
**  COPYRIGHT    :  (c) 2004 .Xiamen Yaxon NetWork CO.LTD       *
**                                                              *
**                                                              *
**              2004/10/21                                      *
****************************************************************/
#ifndef DEF_INCLUDES
#define DEF_INCLUDES

#include <stdio.h>
#include <string.h>
#include <math.h>
#include <stdlib.h>
#include "string.h"

#include "yx_debugcfg.h"//must put it before  yx_swconfig.h
#include "yx_swconfig.h"
#include "yx_system.h"
#include "yx_structs.h"
#include "yx_message.h"

#include "yx_debugcfg.h"
#include "yx_version.h"
#include "yx_msgman.h"
#include "yx_diagnose.h"
#include "yx_debug.h"
#include "yx_tools.h"
#include "port_constvar.h"
#include "yx_debug.h"

#endif /*DEF_INCLUDES*/

