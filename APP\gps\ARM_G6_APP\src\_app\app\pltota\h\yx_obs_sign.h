#ifndef YX_OBS_SIGN
#define YX_OBS_SIGN

#define OBS_MAX_KEY     20
#define OBS_MAX_VALUE     50
#define OBS_HEADERS_NUM  6
#define OBS_PARAMS_NUM  1
#define OBS_MAX_DATE    32


typedef struct{
	char k[OBS_MAX_KEY];
	char v[OBS_MAX_VALUE];
} KV;

typedef struct{
	KV kvs;
	int size;
} KeyValueList;


int get_string_to_sign(char const *method, KV const *headers, KV const *params, char const *bucket, char const *object, long expires, char **output);
int get_signature_for_header_auth(char const *sk, char const *string_to_sign, char *output);
int get_signature_for_url_auth(char const *sk, char const *string_to_sign, char **output);

#endif

