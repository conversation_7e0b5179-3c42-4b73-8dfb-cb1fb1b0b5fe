ifeq ($(PARAM_FILE), )
     PARAM_FILE:=../Makefile.param
     include $(PARAM_FILE)
endif

ifeq ($(APP_PARAM_FILE), )
     APP_PARAM_FILE:=../Makefile.base
     include $(APP_PARAM_FILE)
endif

#define complier
ARM_COMPILER = arm-oe-linux-gnueabi-

# config version
PLATFORM=
APPTYPE=gps_app.exe
VERSION=

# target
TARGET		:= $(strip $(PLATFORM))$(strip $(APPTYPE))$(strip $(VERSION))

# define build template, app / lib
TEMPLATE	:= app

# define configure, static / shared if lib
CONFIG		+= static

# default install dir
BINDIR		?= $(APP_PATH)/APP/build/
	
# external libraries
LIBS		+= -lpthread ../fk/_build/libframework.a ../hal/_build/libGPS_HAL.a
LIBS		+= ../resman_if/_build/libresman_if.a ../ril_if/_build/libril_if.a ../fk/_build/libframework.a

LIBS		+= -l_3515Acomuserv
LIBS		+= -lm

LIBS        += -L../maint_if/_build/ -lmaint_if

LIBS            += -lql_peripheral     \
                   -lqcmap_client      \
                   -lql_common_api     \
                   -lstdc++            \
                   -ldsi_netctrl       \
                   -ldsutils           \
                   -lqmiservices       \
                   -lqmi_cci           \
                   -lqmi_common_so     \
                   -lqmi               \
                   -lmcm               \
                   -lql_tts_client     \
                   -lql_mgmt_client    \
                   -lql_lib_audio      
				   
LIBS		+= -L ./flash_app/ -lrtcm                        
LIBS		+= -L ./usrdata/ -lioTSecuSrv  
LIBS		+= -L ./usrdata/ -lgmssl -lgmcrypto
# defines
DEFINES		+= 

# compile flags
#CFLAGS		+= -Wall -O2 -Wno-uninitialized -fno-strict-aliasing
CFLAGS		+= -mapcs -rdynamic  -funwind-tables  -W -O2 -Wno-uninitialized -fno-strict-aliasing  -Wl,-Map,test.map -ldl 

#LDFLAGS    += -L../fk

include ./Makefile.inc
#include $(APP_PATH)/gsmat/ext_make/Makefile.pathgsm
include ./Makefile.template

