#ifndef _IOTSECUSRV_HASH_HEADER_H_
#define _IOTSECUSRV_HASH_HEADER_H_

#include "iotsecusrv_def.h"

#ifdef __cplusplus
extern "C" {
#endif

	/*
	* brief digest buffer
	*
	* @param buf digest buffer
	* @param buflen the buffer len for digest
	* @param md type for digest
	* @param dgst digest buf with hex
	* @param len digest buf len
	*
	* @return 0 - success ,other - failed
	*
	*/
	int szitrus_digest_buf(
		const char* buf,
		const int buflen,
		const char* datatype,
		const DIGEST_TYPE md,
		char** dgst,
		int* len
	);

	/*
	* brief digest file
	*
	* @param path digest file for path
	* @param md type for digest
	* @param dgst digest buf with hex 
	* @param len digest buf len
	*
	* @return 0 - success ,other - failed
	*
	*/
	int szitrus_digest_file(
		const char* path, 
		const DIGEST_TYPE md, 
		char** dgst, 
		int* len
	);

#ifdef __cplusplus
}
#endif


#endif

