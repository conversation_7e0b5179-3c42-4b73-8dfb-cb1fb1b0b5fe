#ifndef OPENSSL_YX_SSL_H
#define OPENSSL_YX_SSL_H
#if 0
#include <pthread.h>

#define GET_CHIP_ID 					0X01
#define SET_CHIP_ID 					0X21
#define KEEP_ON_RECORD_SUCCESS			0X02
#define GET_PUBLIC_KEY					0X03
#define SM2_SIGN						0X04
#define SM2_VERIFY						0X05
#define SM1_ENCRYPT						0X06
#define SM1_DECRYPT						0X07
#define SM2_ENCRYPT						0X08
#define SM2_DECRYPT						0X09
#define SM3_DIGEST						0X0A
#define SM4_ENCRYPT						0X0B
#define SM4_DECRYPT						0X0C
#define SHA256_DIGEST					0X0D
#define YXAES_ENCRYPT					0X0E
#define YXAES_DECRYPT					0X0F
#define RSA_SIGN						0X10
#define RSA_VERIFY						0X11
#define RSA_ENCRYPT						0X12
#define RSA_DECRYPT						0X13
#define GET_VERSION 					0X14
#define OTP_REQUEST 					0X15
#define IS_CERT_PROBLEM					0X16

typedef struct yx_ssl_st {
	char *ip;
	int port;
	char *rootCert; 
	char *signCertFile;
	char *encryptCertFile;
 	char *encryptKeyFile;
	int type;
	int socketFd;
	int isConncet;//0x00:连接成功 0x01:连接认证失败 0x1000:加载根证书失败 0x10000：加载证书失败 0x100000:加载私钥失败
	void *ctx;
	void *ssl;
	void (*ssl_calback)(struct yx_ssl_st*);
	pthread_t thread;
} YX_SSL;//ssl握手认证返回的结构体

typedef struct yx_sign_st {
	unsigned char *R;//签名R值
	int RLen;//签名R值长度
	unsigned char *S;//签名S值
	int SLen;//签名S值长度
} YX_SIGN;//签名结果值

typedef struct yx_gm_result_st {
	int sequence;//流水号
	void *arg;//加密值（返回unsigned char* 或 结构体，根据type判断）
	int result;//加密结果(<=0 表示失败 >0 表示返回长度或者返回成功)
	int type;//加密类型
} YX_GM_RESULT;

/********************************************************
 **函数名：yx_is_cert_problem
 **函数描述：判断证书是否有问题
 **参数：[in] signFile 签名证书路径
 **返回：0：证书与密钥都正常，不做处理
 **1：证书损坏，重新进行otp
 **2：证书过期重新进行otp
 **3：证书不在有效期，终端时间校准
 **4：芯片私钥与证书不匹配
 **5：未知错误
 ********************************************************/
void yx_is_cert_problem(int sequence, char *signFile, void (*calback)(YX_GM_RESULT *));


/********************************************************
 **函数名：yx_gm_init
 **函数描述：加密初始化
 **返回：
 ********************************************************/
void yx_gm_init();

/********************************************************
 **函数名：yx_gm_init
 **函数描述：加密释放
 **返回：
 ********************************************************/
void yx_gm_free();

/********************************************************
 **函数名：open_seril_port
 **函数描述：打开串口
 **参数：[in] path 串口号
 **     [in] baudrate 波特率
 **返回： 串口操作句柄
 ********************************************************/
int open_seril_port(char *path, int baudrate);

/********************************************************
 **函数名：close_seril_port
 **函数描述:关闭串口
 **参数：[in] fd 串口操作句柄
 **返回： 串口是否关闭成功
 ********************************************************/
int close_seril_port(int fd);

/********************************************************
**函数名：yx_get_chip_id
**函数描述：获取加密芯片ID
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_get_chip_id(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_get_version
**函数描述：获取加密芯片版本号
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_get_version(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_set_chip_id
**函数描述：设置加密芯片ID
**参数：sequence 流水号
**     [in]id
**     [in]idLen id长度（取值范围：1~128） 
** 	   [calback] 回调函数
**返回：
********************************************************/
void yx_set_chip_id(int sequence, unsigned char*id, int idLen, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_key_on_record_success
**函数描述：备案成功通知加密芯片把公钥私钥作为签名验签使用的密钥
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_keep_on_record_success(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_get_public_key
**函数描述：获取加密芯片公钥
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_get_public_key_new(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_get_public_key_once
**函数描述：获取加密芯片公钥(加密芯片已经存在就获取失败)
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_get_public_key(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_sm2_sign
**函数描述：sm2数据签名
**参数：sequence 流水号
**      [in] data 明文数据
**      [in] dataLen 明文数据长度 （范围：1～1200）
**	    [calback] 回调函数
**返回：
********************************************************/
void yx_sm2_sign(int sequence, unsigned char *data, int dataLen, unsigned char *id, int idLen, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm2_verify
 **函数描述：sm2验签
 **参数：sequence 流水号
 **      [in] data 明文数据
 **      [in] dataLen 明文数据长度（范围：1～1200）
 **		 [in] R 签名R值
 **	     [in] RLen 签名R值长度（正常值是32）
 **      [in] S 签名S值
 **      [in] SLen 签名S值长度（正常值是32）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm2_verify(int sequence, unsigned char *data, int dataLen, unsigned char *R, int RLen, unsigned char *S, int SLen,
	 unsigned char *id, int idLen,  void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_otp_request
 **函数描述：otp证书下载（公钥私钥内部随机生成）
 **参数：vehicleId 终端标识号
 **      [in] chipID 芯片ID
 **      [in] url 证书下载地址
 **		 [in] rootFile 根证书存储地址
 **	     [in] signCertFile 签名证书存储地址
 **      [in] encryptCertFile 加密证书存储地址
 **      [in] encryptKeyFile 加密密钥存储地址
 **	     [calback] 回调函数
 **返回：1成功 0：失败（已经请求了，等请求结束再次请求）
 ********************************************************/
int yx_otp_request(char *vehicleId, char *chipID, char *url, char *rootFile, char *signCertFile, char *encryptCertFile,
	char *encryptKeyFile, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_otp_request_with_publickey
 **函数描述：otp证书下载（公钥私钥外部已经调用生成）
 **参数：vehicleId 终端标识号
 **      [in] chipID 芯片ID
 **      [in] url 证书下载地址
 **      [in] publicKey 公钥值
 **      [in] publicKeyLen 公钥长度（正常值是64）
 **		 [in] rootFile 根证书存储地址
 **	     [in] signCertFile 签名证书存储地址
 **      [in] encryptCertFile 加密证书存储地址
 **      [in] encryptKeyFile 加密密钥存储地址
 **	     [calback] 回调函数
 **返回：1成功 0：失败（已经请求了，等请求结束再次请求）
 ********************************************************/
int yx_otp_request_with_publickey(char *vehicleId, char *chipID, char *url, unsigned char*publicKey, int publicKeyLen, 
 char *rootFile, char *signCertFile, char *encryptCertFile, char *encryptKeyFile, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_bds_key_on_record_success（北斗专用）
**函数描述：备案成功通知加密芯片把公钥私钥作为签名验签使用的密钥
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_bds_keep_on_record_success(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_bds_get_public_key_new（北斗专用）
**函数描述：获取加密芯片公钥(加密芯片已经存在就获取失败)
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_bds_get_public_key_new(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_bds_get_public_key_once（北斗专用）
**函数描述：获取加密芯片公钥
**参数：sequence 流水号
**     [calback] 回调函数
**返回：
********************************************************/
void yx_bds_get_public_key(int sequence, void (*calback)(YX_GM_RESULT *));

/********************************************************
**函数名：yx_bds_sm2_sign（北斗专用）
**函数描述：sm2数据签名
**参数：sequence 流水号
**      [in] data 明文数据
**      [in] dataLen 明文数据长度 （范围：1～1200）
**	    [calback] 回调函数
**返回：
********************************************************/
void yx_bds_sm2_sign(int sequence, unsigned char *data, int dataLen, unsigned char *id, int idLen, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_bds_sm2_verify
 **函数描述：sm2验签
 **参数：sequence 流水号
 **      [in] data 明文数据
 **      [in] dataLen 明文数据长度（范围：1～1200）
 **		 [in] R 签名R值
 **	     [in] RLen 签名R值长度（正常值是32）
 **      [in] S 签名S值
 **      [in] SLen 签名S值长度（正常值是32）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_bds_sm2_verify(int sequence, unsigned char *data, int dataLen, unsigned char *R, int RLen, unsigned char *S, int SLen,
	 unsigned char *id, int idLen,  void (*calback)(YX_GM_RESULT *));


/********************************************************
 **函数名：yx_ssl_connect_asyn
 **函数描述：ssl异步握手连接
 **参数：[in] socketFd 已连上的socket
 **		 [in] rootFile 根证书存储地址
 **	     [in] signCertFile 签名证书存储地址
 **      [in] encryptCertFile 加密证书存储地址
 **      [in] encryptKeyFile 加密密钥存储地址
 **     [in] type 请求方式（5:从证书管理中心下载证书保存本地）
 **     [in] ssl_calback异步请求回调函数
 **返回： 是否连接成功
 ********************************************************/
int yx_ssl_connect_asyn(int socketFd, char *rootCert, char *signCertFile, char *encryptCertFile,
 char *encryptKeyFile, int type, void (*ssl_calback)(YX_SSL*));

void yx_close_socket(YX_SSL *yxSsl);

/********************************************************
 **函数名：yx_ssl_connect
 **函数描述：ssl同步握手连接
 **参数：[in] socketFd 已连上的socket
 **		 [in] rootFile 根证书存储地址
 **	     [in] signCertFile 签名证书存储地址
 **      [in] encryptCertFile 加密证书存储地址
 **      [in] encryptKeyFile 加密密钥存储地址
 **     [in] type 请求方式（5:从证书管理中心下载证书保存本地）
 **返回： 是否连接成功
 ********************************************************/
YX_SSL *yx_ssl_connect(int socketFd, char *rootCert, char *signCertFile, char * encryptCertFile, char *encryptKeyFile, int type);

/********************************************************
 **函数名：yx_ssl_write
 **函数描述：写数据
 **参数：[in] ssl
 ** 	[in] data 数据
 **     [in] len 数据长度（范围：1～1100）
 **返回： 已写数据的长度
 ********************************************************/
int yx_ssl_write(YX_SSL *yxSsl,unsigned char *data, int length);

/********************************************************
 **函数名：yx_ssl_read
 **函数描述：读数据
 **参数：[in] ssl
 **		[in] buffer 存储缓存区
 **     [in] 读取最大长度（范围：1～1100）
 **返回： 已读数据长度
 ********************************************************/
int yx_ssl_read(YX_SSL *yxSsl,unsigned char *data, int maxLength);

/********************************************************
 **函数名：yx_ssl_close
 **函数描述：ssl关闭
 **参数：[in] ssl
 **返回：是否关闭成功
 ********************************************************/
int yx_ssl_close(YX_SSL *yxSsl);

/********************************************************
 **函数名：serial_port_init
 **函数描述：串口参数初始化（使用加密芯片就得初始化）
 **参数：[in] path 串口地址
 **      [in] baudrate 串口波特率
 **返回：是否关闭成功
 ********************************************************/
void serial_port_init(char *path, int baudrate);

/********************************************************
 **函数名：otp_init
 **函数描述：otp请求参数初始化（使用otp从服务器获取证书就得初始化）
 **参数：[in] vehicleId 车辆标识符
 **      [in] otpUrl 请求http地址
 **      [in] chipid 芯片ID
 **返回：是否关闭成功
 ********************************************************/
void otp_init(char* vehicleId, char *otpUrl, char *chipid);

/********************************************************
 **函数名：yx_ssl_init
 **函数描述：ssl请求参数初始化
 **参数：
 **返回：
 ********************************************************/
void yx_ssl_init(void);

/********************************************************
 **函数名：yx_sm1_encrypt
 **函数描述：sm1硬件加密
 **参数：sequence 流水号
 **      [in] data 待加密的明文
 **      [in] dataLen 明文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值(传空用ECB)
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm1_encrypt(int sequence, unsigned char *data, int dateLen, unsigned char *key,
 int keyLen, unsigned char *iv, int ivLength, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm1_derypt
 **函数描述：sm1硬件解密
 **参数：sequence 流水号
 **      [in] encrypt 待解密的密文
 **      [in] encryptLen 密文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值(传空用ECB)
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm1_decrypt(int sequence, unsigned char *encrypt, int encryptLen, unsigned char *key,
 int keyLen, unsigned char *iv, int ivLength, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm2_encrypt
 **函数描述：sm2加密
 **参数：sequence 流水号
 **      [in] data 待加密的明文
 **      [in] dataLen 明文长度（范围：1～1200）
 **      [in] key 私钥
 **      [in] keyLen 私钥长度(固定16字节)
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm2_encrypt(int sequence, unsigned char *data, int dataLen,
 unsigned char *publicKey, int keyLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm2_derypt
 **函数描述：sm2解密
 **参数：sequence 流水号
 **      [in] encrypt 待解密的密文
 **      [in] encryptLen 密文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(正常值64字节)
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm2_decrypt(int sequence, unsigned char *encrypt, int encryptLen,
 unsigned char *privateKey, int keyLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm3_digest
 **函数描述：sm3求摘要
 **参数：sequence 流水号
 **      [in] data 待求摘要的数据
 **      [in] dataLen 数据长度（范围：1～2048）
 **      [in] type 摘要方式（0：软摘要， 其他：硬摘要）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm3_digest(int sequence, unsigned char *data, int dateLen, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm4_encrypt
 **函数描述：sm4加密
 **参数：sequence 流水号
 **      [in] data 待加密的明文
 **      [in] dataLen 明文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值（传空用ECB）
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sm4_encrypt(int sequence, unsigned char *data, int dateLen, unsigned char *key, int keyLen,
 unsigned char *iv, int ivLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sm4_derypt
 **函数描述：sm4解密
 **参数：sequence 流水号
 **      [in] encrypt 待解密的密文
 **      [in] encryptLen 密文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值（传空用ECB）
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **      [in] type 解密方式（0：软解密， 其他：硬解密）
 **	     [calback] 回调函数
 **返回
 ********************************************************/
void yx_sm4_decrypt(int sequence, unsigned char *encrypt, int encryptLen, unsigned char *key, 
 int keyLen, unsigned char *iv, int ivLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_sha256_digest
 **函数描述：sha256求摘要
 **参数：sequence 流水号
 **      [in] data 待求摘要的数据
 **      [in] dataLen 数据长度（范围：1～2048）
 **      [in] type 摘要方式（0：软摘要， 其他：硬摘要）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_sha256_digest(int sequence, unsigned char *data, int dataLen, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_aes_encrypt
 **函数描述：aes加密
 **参数：sequence 流水号
 **      [in] data 待加密的明文
 **      [in] dataLen 明文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值（传空用ECB）
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_aes_encrypt(int sequence, unsigned char *data, int dataLen, unsigned char *key,
 int keyLen, unsigned char *iv, int ivLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_aes_derypt
 **函数描述：aes解密
 **参数：sequence 流水号
 **      [in] encrypt 待解密的密文
 **      [in] encryptLen 密文长度（范围：1～1200）
 **      [in] key 密钥
 **      [in] keyLen 密钥长度(固定16字节)
 **      [in] iv CBC加密用到的iv值（传空用ECB）
 **      [in] ivLength iv长度（固定16字节,不是16字节用ECB）
 **      [in] type 解密方式（0：软解密， 其他：硬解密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_aes_decrypt(int sequence, unsigned char *encrypt, int encryptLen, unsigned char *key, 
 int keyLen, unsigned char *iv, int ivLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_rsa_sign
 **函数描述：rsa签名
 **参数：sequence 流水号
 **      [in] sign 签名存储的签名值
 **      [in] dgst 待签名的摘要
 **      [in] dgstLen 摘要长度（范围：1～1200）
 **      [in] publicKey 公钥
 **      [in] publickeyLength 公钥长度（范围：1～255）
 **      [in] privateKey 私钥
 **      [in] keyLength 私钥长度（范围：1～255）
 **      [in] type 签名方式(0:软签名，其他：硬签名)
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_rsa_sign(int sequence, unsigned char *dgst, int dgstLen, unsigned char *publicKey, int publickeyLength,
 unsigned char *privateKey, int keyLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_rsa_verify
 **函数描述：rsa验签
 **参数：sequence 流水号
 **      [in] dgst 摘要值
 **      [in] dgstLen 摘要长度（范围：1～1200）
 **		 [in] sign 签名值
 **      [in] signLen 签名值长度（范围：1～255）
 **      [in] publicKey 公钥
 **      [in] keyLength 公钥长度（范围：1～255）
 **      [in] type 验签方式(0:软验签，其他：硬验签)
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_rsa_verify(int sequence, unsigned char *dgst, int dgstLen, unsigned char *sign, int signLen,
 unsigned char *publicKey, int keyLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_rsa_encrypt
 **函数描述：rsa加密
 **参数：sequence 流水号
 **      [in] data 待加密的明文
 **      [in] dataLen 明文长度（范围：1～1200）
 **      [in] publicKey 公钥
 **      [in] publickeyLength 公钥长度（范围：1～255）
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_rsa_encrypt(int sequence, unsigned char *data, int dataLen, unsigned char *publicKey,
 int keyLength, int type, void (*calback)(YX_GM_RESULT *));

/********************************************************
 **函数名：yx_rsa_derypt
 **函数描述：rsa解密
 **参数：sequence 流水号
 **      [in] encrypt 待解密的密文
 **      [in] encryptLen 密文长度（范围：1～1200）
 **      [in] publicKey 公钥
 **      [in] publickeyLength 公钥长度（范围：1～255）
 **      [in] key 私钥
 **      [in] keyLen 私钥长度（范围：1～255）
 **      [in] type 加密方式（0：软加密， 其他：硬加密）
 **	     [calback] 回调函数
 **返回：
 ********************************************************/
void yx_rsa_decrypt(int sequence, unsigned char *encrypt, int encryptLen, unsigned char *publicKey, int publickeyLength,
 unsigned char *privateKey, int keyLength, int type, void (*calback)(YX_GM_RESULT *));

#endif
#endif
