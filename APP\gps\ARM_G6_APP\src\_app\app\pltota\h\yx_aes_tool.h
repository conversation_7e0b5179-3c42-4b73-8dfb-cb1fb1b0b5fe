﻿
#ifndef YX_AES_TOOL_H
#define YX_AES_TOOL_H

//定义整个DLL中所需要的全部函数
void SubBytes(unsigned char state[][4]);
void ShiftRows(unsigned char state[][4]);//行移位变换
void MixColumns(unsigned char state[][4]);//列混淆变换
unsigned char FFmul(unsigned char a, unsigned char b);
void AddRoundKey(unsigned char state[][4], unsigned char k[][4]);//轮密钥加变换
void KeyExpansion(unsigned char* key, unsigned char w[][4][4]);//密钥扩展
//解密的基本运算
void InvSubBytes(unsigned char state[][4]);//逆字节替代
void InvShiftRows(unsigned char state[][4]);//逆行移位
void InvMixColumns(unsigned char state[][4]);//逆列混淆
//加密过程:先将输入的明文按列序组合成4*4的矩阵，直接与第0组密钥（即输入的密钥）相加（异或），作为轮加密的输入
//然后循环10次进行SubBytes、ShiftRows、MixColumns、AddRoundKey运算，最后恢复原序列
//需要注意的是最后一轮并不进行MixColumns（列混淆变换）
unsigned char* Cipher(unsigned char* input,unsigned char w[][4][4]);
//解密过程
unsigned char* InvCipher(unsigned char* input, unsigned char w[][4][4]);
//简要加密
unsigned char* CipherSimple(unsigned char* input, unsigned char w[][4][4]);
//简要解密
unsigned char* InvCipherSimple(unsigned char* input, unsigned char w[][4][4]);
//加密字符串
void* CipherString(void* input, int length,unsigned char* key);
//解密字符串
void* InvCipherString(void* input, int length,unsigned char* key);

//加密文件
int CipherFile(char *plainFile,unsigned char* key,char *cipherFile);
//解密文件
int InvCipherFile(char *cipherFile,unsigned char* key,char *plainFile);

//简要加密文件
int CipherFileSimple(char *plainFile,unsigned char* key,char *cipherFile);
//用于解密key文件
int InvCipherFileSimple(unsigned char * pCipherSrc, unsigned int srcLen, unsigned char* key, unsigned int *pPlainLen);

//用于解密pkg文件
int InvCipherFile2FileProgressive(YX_FILE * pSrcFile, YX_FILE * pDstFile, unsigned int srcLen, unsigned char* key, unsigned int *pPlainLen);

#endif
