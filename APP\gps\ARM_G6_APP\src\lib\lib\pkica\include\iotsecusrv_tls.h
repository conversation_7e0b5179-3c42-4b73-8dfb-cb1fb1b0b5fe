#ifndef _IOTSECUSRV_TLS_HEADER_H_
#define _IOTSECUSRV_TLS_HEADER_H_

#include "iotsecusrv_def.h"

#ifdef __cplusplus
extern "C" {
#endif
	//2021 10 01 add tls 
	/*
	* brief TLS init with tls server only
	*
	* @param sync socket sync
	* @param dm dest ip | domain
	* @param port dest port info
	* @param h return tls handle
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_init(
		const int sync,
		const char* dm,
		const int port,
		unsigned long * h
	);

	/*
	* brief TLS init for file
	*
	* @param cert_path   the file PEM cert path
	* @param key_path    the file PEM_KEY path with encrypted
	* @param pwd  the key for key file decrypte
	* @param key_type type for key | RSA | SM2
	* @param sync socket sync
	* @param dm dest ip | domain
	* @param port dest port info
	* @param h return tls handle
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_init_file(
		const char* cert_path,
		const char* key_path,
		const char* e_cert_path,
		const char* e_key_path,
		const char* pwd,
		const KEY_TYPE key_type,
		const int sync,
		const char* dm,
		const int port,
		unsigned long * h
	);

	/*
	* brief TLS init for buf
	*
	* @param cert_buf   base64 encoded cert buf ,without Begin certification ,----- END CERTIFICATION ----- ,without \r\n
	* @param cert_len   cert_buf size
	* @param key_buf    base64 encoded key buf ,without Begin certification ,----- END CERTIFICATION ----- ,without \r\n
	* @param key_len    key_buf size
	* @param pwd  the key for key file decrypte
	* @param key_type type for key | RSA | SM2
	* @param sync socket sync
	* @param dm dest ip | domain
	* @param port dest port info
	* @param h return tls handle
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_init_buffer(
		const char* cert_buf,
		const int cert_len,
		const char* key_buf,
		const int key_len,
		const char* e_cert_buf,
		const int e_cert_len,
		const char* e_key_buf,
		const int e_key_len,
		const KEY_TYPE key_type,
		const int sync,
		const char* dm,
		const int port,
		unsigned long * h
	);

	/*
	* brief TLS init for chiper
	*
	* @param cert_path   the file PEM cert path
	* @param index   chip index
	* @param key_type type for key | RSA | SM2
	* @param sync socket sync
	* @param dm dest ip | domain
	* @param port dest port info
	* @param h return tls handle
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_init_chiper(
		const char* cert_path,
		const char* e_cert_path,
		const int index,
		const KEY_TYPE key_type,
		const int sync,
		const char* dm,
		const int port,
		unsigned long * h
	);

	/*
	* brief TLS send buf
	*
	* @param h the handle for tls
	* @param buf  send buf
	* @param len  buf size
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_send(
		const unsigned long h,
		const char* buf,
		const int len
	);

	/*
	* brief TLS recv buf
	*
	* @param h the handle for tls
	* @param buf  recv buf
	* @param len  recv size
	*
	* @return 0 - success ,other - failed
	*/
	int szitrus_tls_recv(
		const unsigned long h,
		char** buf,
		int* len
	);


	/*
	* brief TLS close handle
	*
	* @param h the handle for tls
	*
	* @return 0 - success ,other - failed
	*
	*/
	int szitrus_tls_close(
		const unsigned long h
	);


#ifdef __cplusplus
}
#endif


#endif
