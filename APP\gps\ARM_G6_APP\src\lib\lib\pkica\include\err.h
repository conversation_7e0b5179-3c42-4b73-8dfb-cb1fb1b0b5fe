#ifndef _ERR_
#define _ERR_

#define ERROR_SUCCESS 0x00
#define ERROR_PARAM -0x01


#define ERROR_SOCKET -0x03  //create local socket error to connect ioTSecuSrv
#define ERROR_CONNECT -0x04//socket error to connect ioTSecuSrv
#define ERROR_SEND -0x05 // socket error to send buf to ioTSecuSrv
#define ERROR_RECV -0x06//socket error recv buf from ioTSecuSrv
#define ERROR_TIMEOUT -0x07 //socket timeout to connect 

#define ERROR_JSON -0x08  //ioTSecuSrv return buf errro

#define ERROR_MALLOC -0x09 //no memery to recv data
#define ERROR_MORE_DATA -0x0A //no more data to recv buf
#define ERROR_SUPPORT -0x0B  //no type to support

#define ERROR_TLS_HANDLE -0x10  //no handle for tls
#define ERROR_TLS_DATA -0x11  //no data to recv from tls

#define ERROR_VERIFY_KEY -0x13 //public key error
#define ERROR_VERIFY_SRC -0x14 //verify plait error
#define ERROR_SIGN_CERT -0x15 //sign cert error


#endif
