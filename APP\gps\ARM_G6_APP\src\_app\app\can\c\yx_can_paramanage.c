#include "yx_includes.h"
#include "yx_tools.h"
#include "yx_stream.h"
#include "yx_sysframe.h"
#include "yx_protocol_type.h"
#include "yx_rx_frame.h"
#include "yx_pp_core.h"
#include "yx_posrept.h"
#include "yx_alarmer.h"
#include "yx_pp_misc.h"
#include "dal_gprs_drv.h"
#include "dal_systime.h"
#include "yx_wintrans.h"
#include "yx_dm.h"
#include "yx_120r_drv.h"

#include "yx_ttscore.h"

/*
********************************************************************************
*                  DEFINE MODULE VARIANTS
********************************************************************************
*/
static STREAM_T s_rstrm;
static INT32U s_hdlframelen;
static INT32U s_channeltype;
static SYSFRAME_T *s_hdlframe;

static LOW_GEAR_T s_lowgear;
static VEHICHE_ALARM_T s_vehiche_acc;
static FULL_THROTTLE_T s_fullthrottle;
static JL_IDLING_T s_idling;
static LQ_OVERSPEED_T s_overspeed;
static SPEEDALMPARA_T s_speedalmpara;
static INT8U s_ackstream[10];
static BOOLEAN s_onceagain = false;

/*******************************************************************
** ???:      HdlMsg_DN_CMD_SETPARA
** ????:    MSG_8103 ??????
** ??:        [in]  NULL
** ??:        NULL
********************************************************************/
static void HdlMsg_DN_CMD_CAN_SETPARA(void)
{
    INT8U paranum,i;
    INT16U paraid;
    INT8U paralen;
    BOOLEAN accelset = FALSE;
    
    YX_ReadPubParaByID(LOW_GEAR_,          (INT8U *)&s_lowgear,           sizeof(LOW_GEAR_T));
    YX_ReadPubParaByID(VEHICHE_ALARM_,   (INT8U *)&s_vehiche_acc,   sizeof(VEHICHE_ALARM_T));
    YX_ReadPubParaByID(FULL_THROTTLE_,   (INT8U *)&s_fullthrottle,       sizeof(FULL_THROTTLE_T));
    YX_ReadPubParaByID(JL_IDLING_,            (INT8U *)&s_idling,               sizeof(JL_IDLING_T));
    YX_ReadPubParaByID(OVER_SPEED_,       (INT8U *)&s_overspeed,       sizeof(LQ_OVERSPEED_T));

    paranum = YX_ReadBYTE_Strm(&s_rstrm);
    
    for (i = 0; i <paranum; i++) {
        if (YX_GetStrmLeftLen(&s_rstrm) >= 3) {
            paraid = YX_ReadHWORD_Strm(&s_rstrm);
        } else {
            return;
        }
        
        paralen = YX_ReadBYTE_Strm(&s_rstrm);
        switch (paraid) {
            case 0x1001:
                if (paralen != 2) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 2) {
                    s_lowgear.gearmax = YX_ReadBYTE_Strm(&s_rstrm);
                    s_lowgear.speedmax = YX_ReadBYTE_Strm(&s_rstrm);
                } else return;
                YX_StorePubParaByID(LOW_GEAR_,   (INT8U *)&s_lowgear,     sizeof(LOW_GEAR_T));
                break;
            case 0x1002:
                if (paralen != 2) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 2) {
                    s_vehiche_acc.accel[0].lmtacc = (float)YX_ReadBYTE_Strm(&s_rstrm);
                    s_vehiche_acc.accel[0].staytime = YX_ReadBYTE_Strm(&s_rstrm);
                    accelset = TRUE;
                } else return;
                break;
            case 0x1003:
                if (paralen != 2) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 2) {
                    s_vehiche_acc.decel[0].lmtacc = (float)YX_ReadBYTE_Strm(&s_rstrm);
                    s_vehiche_acc.decel[0].staytime = YX_ReadBYTE_Strm(&s_rstrm);
                    accelset = TRUE;
                } else return;
                break;
            case 0x1004:
                if (paralen != 1) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 1) {
                    s_fullthrottle.staytime = YX_ReadBYTE_Strm(&s_rstrm);
                } else return;
                    YX_StorePubParaByID(FULL_THROTTLE_,   (INT8U *)&s_fullthrottle,     sizeof(FULL_THROTTLE_T));
                break;
            case 0x1005:
                if (paralen != 1) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 1) {
                    s_idling.jl_idling_tlimit = YX_ReadBYTE_Strm(&s_rstrm) * 60;
                } else return;
                YX_StorePubParaByID(JL_IDLING_, (INT8U *)&s_idling, sizeof(JL_IDLING_T));
                break;
            case 0x1006:
                if (paralen != 2) return;
                if (YX_GetStrmLeftLen(&s_rstrm) >= 2) {
                    s_overspeed.staytime = YX_ReadBYTE_Strm(&s_rstrm);
                    s_overspeed.speedmax = YX_ReadBYTE_Strm(&s_rstrm);
                } else return;
                YX_StorePubParaByID(OVER_SPEED_, (INT8U *)&s_overspeed, sizeof(LQ_OVERSPEED_T));
                break;
            case 0x1007:
                if (paralen != 1) return;
                YX_MEMSET((INT8U *)&s_speedalmpara, 0, sizeof(SPEEDALMPARA_T));
                YX_ReadPubParaByID(SPEEDALMPARA_, (INT8U *)&s_speedalmpara, sizeof(SPEEDALMPARA_T));
                s_speedalmpara.limitspeed = YX_ReadBYTE_Strm(&s_rstrm);
                YX_StorePubParaByID(SPEEDALMPARA_, (INT8U *)&s_speedalmpara, sizeof(SPEEDALMPARA_T));
                break;
            default:
                break;
        }
    }
    
    if (accelset == TRUE) {
        YX_StorePubParaByID(VEHICHE_ALARM_,   (INT8U *)&s_vehiche_acc,     sizeof(VEHICHE_ALARM_T));
    }
    YX_AsmCommonAck(s_hdlframe, s_channeltype, _SYSFRAME_ACK);
}

/*******************************************************************
** ???:     Callback_SendResult
** ????:   ?????????????????
** ??:       [in] winid:  ??ID
**             [in] result: ??
**             [in] ptr:    ????
**             [in] len:    ????
** ??:       ?
********************************************************************/
static void Callback_SendResult(INT16U winid, INT8U result, INT8U *ptr, INT32U len)
{
    if (ptr != NULL) {
        YX_MemFree(ptr);
    }
}

/*******************************************************************
** ???:      HdlMsg_DN_CMD_QRYPARA
** ????:    MSG_8104 ??????
** ??:        [in]  NULL                  
** ??:        NULL
********************************************************************/
static void HdlMsg_DN_CMD_CAN_QRYPARA(void)
{
    STREAM_T wstrm;
    INT8U paranum,i, paranum2;
    INT16U paraid;
    INT8U *ptr, *numptr;
    
    YX_ReadPubParaByID(LOW_GEAR_,          (INT8U *)&s_lowgear,           sizeof(LOW_GEAR_T));
    YX_ReadPubParaByID(VEHICHE_ALARM_,   (INT8U *)&s_vehiche_acc,   sizeof(VEHICHE_ALARM_T));
    YX_ReadPubParaByID(FULL_THROTTLE_,   (INT8U *)&s_fullthrottle,       sizeof(FULL_THROTTLE_T));
    YX_ReadPubParaByID(JL_IDLING_,            (INT8U *)&s_idling,               sizeof(JL_IDLING_T));
    YX_ReadPubParaByID(OVER_SPEED_,       (INT8U *)&s_overspeed,       sizeof(LQ_OVERSPEED_T));
    
    ptr = YX_MemMalloc(4096);
    if (ptr != NULL) {
        YX_InitStrm(&wstrm, ptr, 4096);
    } else {
        return;
    }
    
    paranum2 = 0;
    numptr = YX_GetStrmPtr(&wstrm);
    YX_WriteBYTE_Strm(&wstrm, paranum2);
    
    paranum = YX_ReadBYTE_Strm(&s_rstrm);
    
    for (i = 0; i <paranum; i++) {
        if (YX_GetStrmLeftLen(&s_rstrm) >= 2) {
            paraid = YX_ReadHWORD_Strm(&s_rstrm);
        } else {
            break;
        }
        
        switch (paraid) {
            case 0x1001:
                YX_WriteHWORD_Strm(&wstrm, 0x1001);
                YX_WriteBYTE_Strm(&wstrm, 2);
                YX_WriteBYTE_Strm(&wstrm, s_lowgear.gearmax);
                YX_WriteBYTE_Strm(&wstrm, s_lowgear.speedmax);
                paranum2++;
                break;
            case 0x1002:
                YX_WriteHWORD_Strm(&wstrm, 0x1002);
                YX_WriteBYTE_Strm(&wstrm, 2);
                YX_WriteBYTE_Strm(&wstrm, (INT8U)s_vehiche_acc.accel[0].lmtacc);
                YX_WriteBYTE_Strm(&wstrm, s_vehiche_acc.accel[0].staytime);
                paranum2++;
                break;
            case 0x1003:
                YX_WriteHWORD_Strm(&wstrm, 0x1002);
                YX_WriteBYTE_Strm(&wstrm, 2);
                YX_WriteBYTE_Strm(&wstrm, (INT8U)s_vehiche_acc.decel[0].lmtacc);
                YX_WriteBYTE_Strm(&wstrm, s_vehiche_acc.decel[0].staytime);
                paranum2++;
                break;
            case 0x1004:
                YX_WriteHWORD_Strm(&wstrm, 0x1002);
                YX_WriteBYTE_Strm(&wstrm, 1);
                YX_WriteBYTE_Strm(&wstrm, s_fullthrottle.staytime);
                paranum2++;
                break;
            case 0x1005:
                YX_WriteHWORD_Strm(&wstrm, 0x1002);
                YX_WriteBYTE_Strm(&wstrm, 1);
                YX_WriteBYTE_Strm(&wstrm, s_idling.jl_idling_tlimit / 60);
                paranum2++;
                break;
            case 0x1006:
                YX_WriteHWORD_Strm(&wstrm, 0x1002);
                YX_WriteBYTE_Strm(&wstrm, 2);
                YX_WriteBYTE_Strm(&wstrm, s_overspeed.staytime);
                YX_WriteBYTE_Strm(&wstrm, s_overspeed.speedmax);
                paranum2++;
                break;
            default:
                break;
        }
    }
    
    *numptr = paranum2;
    
    if (YX_WT_ReqSendMode1(UP_ACK_CAN_QRYPARA, s_channeltype, WT_PRIO_LOW, YX_GetStrmStartPtr(&wstrm), YX_GetStrmLen(&wstrm), Callback_SendResult) == 0) {
        YX_MemFree(ptr);
    }
}


/*******************************************************************
** ???:      HdlMsg_DN_CMD_CAN_LC
** ????:    ????????
** ??:        [in]  NULL                  
** ??:        NULL
********************************************************************/
static void HdlMsg_DN_CMD_CAN_LC(void)
{
    INT8U event, lockctrl;
    LC_CENTER_T lc_center;
    INT16U seq;
    #if EN_DEBUG > 0
    INT16U psw;
    #endif
    LQ_CONNET_T lqc;
    

    
    #if DEBUG_LOCK > 0
    debug_printf("<0x3F07??????????: ");
    printf_hex(YX_GetStrmStartPtr(&s_rstrm), s_hdlframelen);
    debug_printf(">\r\n");
    #endif

    YX_MEMSET((INT8U *)&lqc, 0, sizeof(LQ_CONNET_T));
    YX_ReadPubParaByID(LQ_CONNET_, (INT8U *)&lqc, sizeof(LQ_CONNET_T));
    if (lqc.lqclw == false) {
        #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"?????? ??????????", 33, P_LOW);
        #endif
        #if DEBUG_LOCK > 0
        debug_printf("<????????>\r\n");
        #endif
        return;
    }
    //addchknum = 0;
    YX_MEMSET((INT8U *)&lc_center, 0, sizeof(LC_CENTER_T));
    lc_center.locktype = YX_ReadBYTE_Strm(&s_rstrm);
    //addchknum = addchknum + lc_center.locktype;
    YX_ReadDATA_Strm(&s_rstrm, lc_center.sos_psw, sizeof(lc_center.sos_psw));
   // addchknum = addchknum + lc_center.sos_psw[1] + lc_center.sos_psw[2];
    lc_center.engine_speed = YX_ReadHWORD_Strm(&s_rstrm);
    //addchknum = addchknum + lc_center.engine_speed; 
    lc_center.engine_torque_per = YX_ReadBYTE_Strm(&s_rstrm);
    //addchknum = addchknum + lc_center.engine_torque_per;
    YX_ReadDATA_Strm(&s_rstrm, lc_center.res, sizeof(lc_center.res));
    
    if ((lc_center.sos_psw[0] == 0) && (lc_center.sos_psw[1] == 0)) {   /*????????????0*/
        lc_center.sos_psw[0] = 0xff;
        lc_center.sos_psw[1] = 0xff;
    }
    
    seq = YX_GetFlowseq_SYSFrame(s_hdlframe);
    if (!YX_110R_IsON() && !YX_120R_IsON()) {
        YX_LC_AckToCtr(seq, lc_center.locktype, 0, LC_POWEROFF);
        return;
    }
  
    #if EN_DEBUG > 0
    psw = (lc_center.sos_psw[0] << 8) + lc_center.sos_psw[1];
    debug_printf("<??:%d,??:%d,??:%d,??:%d>\r\n", lc_center.locktype, psw, lc_center.engine_speed, lc_center.engine_torque_per);
    #endif
    
    event = LC_EVENT_CTR;
    switch (lc_center.locktype) {
        case 1:
            lockctrl = LC_CMD_BIND;
            #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"????????", 16, P_LOW);
            #endif
            if (lqc.lcswitch == false) {
                lqc.lcswitch  = true;
                YX_Inform_PingTaiCmd(true);
                DAL_PP_StoreParaInstantByID(LQ_CONNET_, (INT8U *)&lqc, sizeof(LQ_CONNET_T));
                //YX_Inform_SynEnable();
            }
            break;
        case 2:
            lockctrl = LC_CMD_LOCK;
            return; /*?????????*/
            #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"????????", 16, P_LOW);
            #endif
            break;
        case 3:
            lockctrl = LC_CMD_LSPEED1;
            #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"????????????", 24, P_LOW);
            #endif
  
            break;
        case 4:
            lockctrl = LC_CMD_UNLOCK;
            #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"??????????", 20, P_LOW);
            #endif
            break;
        case 5:
            lockctrl = LC_CMD_UNBIND;
            #if EN_TTS > 0
            YX_PlayTts(TTS_TEXT, (INT8U*)"??????????", 20, P_LOW);
            #endif
         
            break;
        default:
            lockctrl = LC_CMD_UNBIND;
            break;
    }
    
    if (lqc.lcswitch == false) {   /*??????????????*/
        return;
    }
           
    if (lockctrl != LC_CMD_BIND && YX_LC_GetBindflag() != LC_ACT) {
        #if DEBUG_LOCK > 0
        debug_printf("lockctrl:%d, YX_LC_GetBindflag() = %d\r\n",lockctrl, YX_LC_GetBindflag());
        #endif
        YX_LC_AckToCtr(seq, lc_center.locktype, 0, LC_NO_ACK);
        return;
    }
    
    YX_LC_StoreCmd(&lc_center, event, lockctrl);
    
    YX_LC_EventInform(event, lockctrl, seq);
    //YX_AsmCommonAck(s_hdlframe, s_channeltype, _SYSFRAME_ACK); 
}

/*******************************************************************
** ???:      HdlMsg_DN_CMD_CAN_QRYLC
** ????:    ????????
** ??:        [in]  NULL                  
** ??:        NULL
********************************************************************/
static void HdlMsg_DN_CMD_CAN_QRYLC(void)
{
    INT8U *memptr, memlen;
    INT16U winid, seq;
    STREAM_T wstrm;
    BCD_TIME_T bcdtime;
    
    seq = YX_GetFlowseq_SYSFrame(s_hdlframe);
        
    memlen = 100;
    if (NULL == (memptr = YX_MemMalloc(memlen))){
        return;
    }
    
    YX_InitStrm(&wstrm, memptr, memlen);
    YX_WriteHWORD_Strm(&wstrm, seq);
    YX_GetBcdSysTime(&bcdtime);
    YX_WriteDATA_Strm(&wstrm, (INT8U *)&bcdtime, sizeof(BCD_TIME_T));
    YX_MovStrmPtr(&wstrm, YX_LC_QryLCStatus(YX_GetStrmPtr(&wstrm)));
    YX_WriteLONG_Strm(&wstrm, 0);
    
    #if DEBUG_LOCK > 0
    debug_printf("<??????????: ");
    printf_hex(YX_GetStrmStartPtr(&wstrm), YX_GetStrmLen(&wstrm));
    debug_printf(">\r\n");
    #endif
    
    winid = YX_WT_ReqSendMode1(UP_ACK_CAN_QRYLC, s_channeltype, WT_PRIO_HIGH, memptr, YX_GetStrmLen(&wstrm), Callback_SendResult);
    if (winid == 0) {
        YX_MemFree(memptr);
        memptr = NULL;
    }
}

/*******************************************************************
** ???:      HdlMsg_DN_CMD_SET_LC_TIME
** ????:    ??????????
** ??:        [in]  NULL                  
** ??:        NULL
********************************************************************/
static void HdlMsg_DN_CMD_SET_LC_TIME(void)
{
    INT8U *memptr, memlen, day;
    INT16U winid, seq;
    INT32U minute;
    STREAM_T wstrm;
    BCD_TIME_T bcdtime;
    LC_SET_UNLINE_T unline;
    
    #if DEBUG_LOCK > 0
    debug_printf("<0x3F0D????????????: ");
    printf_hex(YX_GetStrmStartPtr(&s_rstrm), s_hdlframelen);
    debug_printf(">\r\n");
    #endif
    
    seq = YX_GetFlowseq_SYSFrame(s_hdlframe);
    minute = YX_ReadLONG_Strm(&s_rstrm);
    day = minute /(24 * 60) ;
    
    
    memlen = 50;
    if (NULL == (memptr = YX_MemMalloc(memlen))){
        return;
    }
    
    YX_InitStrm(&wstrm, memptr, memlen);
    YX_WriteHWORD_Strm(&wstrm, seq);
    YX_GetBcdSysTime(&bcdtime);
    YX_WriteDATA_Strm(&wstrm, (INT8U *)&bcdtime, sizeof(BCD_TIME_T));
    if (day >= 7 && day <= 31 ) {
        YX_MEMSET((INT8U *)&unline, 0, sizeof(LC_SET_UNLINE_T));
        unline.day = day;
        DAL_PP_StoreParaByID(LC_SET_UNLINE_, (INT8U *)&unline, sizeof(LC_SET_UNLINE_T));
        YX_WriteHWORD_Strm(&wstrm, 1);
    } else {
        YX_WriteHWORD_Strm(&wstrm, 2);
    }
    
    #if DEBUG_LOCK > 0
    debug_printf("minute = %d, day = %d\r\n", minute, day);
    debug_printf("<????????????: ");
    printf_hex(YX_GetStrmStartPtr(&wstrm), YX_GetStrmLen(&wstrm));
    debug_printf(">\r\n");
    #endif
    
    winid = YX_WT_ReqSendMode1(UP_ACK_SET_LC_TIME, s_channeltype, WT_PRIO_HIGH, memptr, YX_GetStrmLen(&wstrm), Callback_SendResult);
    if (winid == 0) {
        YX_MemFree(memptr);
        memptr = NULL;
    }
}


#if EN_FREECONST > 0  /* EN_FREECONST > 0 */
static FUNCENTRY_T s_functionentry[] = {
                                         DN_CMD_CAN_SETPARA,            HdlMsg_DN_CMD_CAN_SETPARA,      /* ???? */
                                         DN_CMD_CAN_QRYPARA,            HdlMsg_DN_CMD_CAN_QRYPARA,      /* ???? */
                                         DN_CMD_CAN_LC,                 HdlMsg_DN_CMD_CAN_LC,           /* ???????? */
                                         DN_CMD_CAN_QRYLC,              HdlMsg_DN_CMD_CAN_QRYLC         /* ???????? */
                                         DN_CMD_SET_LC_TIME,              HdlMsg_DN_CMD_SET_LC_TIME         /* ??????????*/
                                     };
static INT8U s_funnum = sizeof(s_functionentry)/sizeof(s_functionentry[0]);

#else /* EN_FREECONST == 0 */

static INT8U s_funnum;
static FUNCENTRY_T s_functionentry[5];
/*
****************************************************************
*   ?????
****************************************************************
*/
DECLARE_GPS_FUN_INIT_CONSTVAR(GPS_CAN_PARA_MANAGE_STATICDAT_ID)
{
    s_funnum = 0;
    
    s_functionentry[s_funnum].index = DN_CMD_CAN_SETPARA;        s_functionentry[s_funnum].entryproc = HdlMsg_DN_CMD_CAN_SETPARA;        s_funnum++;
    s_functionentry[s_funnum].index = DN_CMD_CAN_QRYPARA;        s_functionentry[s_funnum].entryproc = HdlMsg_DN_CMD_CAN_QRYPARA;        s_funnum++;
    
    s_functionentry[s_funnum].index = DN_CMD_CAN_LC;             s_functionentry[s_funnum].entryproc = HdlMsg_DN_CMD_CAN_LC;             s_funnum++;
    s_functionentry[s_funnum].index = DN_CMD_CAN_QRYLC;          s_functionentry[s_funnum].entryproc = HdlMsg_DN_CMD_CAN_QRYLC;          s_funnum++;
    s_functionentry[s_funnum].index = DN_CMD_SET_LC_TIME;          s_functionentry[s_funnum].entryproc = HdlMsg_DN_CMD_SET_LC_TIME;          s_funnum++;
    
    OS_ASSERT((s_funnum <= sizeof(s_functionentry) / sizeof(s_functionentry[0])), RETURN_VOID);
}
#endif /* end EN_FREECONST */
                                     
/*******************************************************************
*   ??: HdlParaManageFrameMsg
*
*   ??: ???????????
*   ??: ???,??,????
*   ??: ?
*   ??: ?
*******************************************************************/
static void HdlParaManageFrameMsg(SYSFRAME_T *curframe, INT32U framelen, INT8U type)
{
    s_hdlframe    = curframe;
    s_hdlframelen = framelen - SYSHEAD_LEN - SYSTAIL_LEN;
    
    YX_RxchannelToTxchannelAttr(type, &s_channeltype);
    
    YX_InitStrm(&s_rstrm, curframe->data, s_hdlframelen);
    YX_FindProcEntry(YX_GetType_SYSFrame(curframe), s_functionentry, s_funnum);
}

/*******************************************************************
*   ??: YX_CAN_InitParaManage
*
*   ??: ?????????
*   ??: ?
*   ??: ?
*   ??: ?
*******************************************************************/
void YX_CAN_InitParaManage(void)
{
    INT8U i;
    
    for (i = 0; i < s_funnum; i++) {
        YX_RegisterProtocolHander(TYPE_PROTOCOL, s_functionentry[i].index, HdlParaManageFrameMsg);
    }   
}

/*******************************************************************
** ???:      YX_LC_AckToCtr
** ????:    ??????????
** ??:        [in]  locktype:         ??????
                [in]  result:           ????
                [in]  code:             ???
** ??:        WINID
********************************************************************/
INT16U YX_LC_AckToCtr(INT16U seq, INT8U locktype, INT8U result, INT16U code)
{
    INT8U *memptr, memlen;
    INT16U winid;
    STREAM_T wstrm;
    
    
    memlen = 50;
    if (NULL == (memptr = YX_MemMalloc(memlen))){
        return 0;
    }
    
    YX_InitStrm(&wstrm, memptr, memlen);
    YX_WriteHWORD_Strm(&wstrm, seq);
    YX_WriteBYTE_Strm(&wstrm, locktype);
    YX_WriteBYTE_Strm(&wstrm, result);
    YX_WriteHWORD_Strm(&wstrm, code);
    YX_WriteLONG_Strm(&wstrm, 0);                   /* ?? */
    
    #if DEBUG_LOCK > 0
    debug_printf("<??????????: ");
    printf_hex(YX_GetStrmStartPtr(&wstrm), YX_GetStrmLen(&wstrm));
    debug_printf(">\r\n");
    #endif
    
    winid = YX_WT_ReqSendMode1(UP_ACK_CAN_LC, s_channeltype, WT_PRIO_HIGH, memptr, YX_GetStrmLen(&wstrm), Callback_SendResult);
    if (winid == 0) {
        YX_MEMSET(&s_ackstream, 0, sizeof(s_ackstream));
        YX_MEMCPY(&s_ackstream, 10, memptr, 10);
        s_onceagain = true;
        YX_MemFree(memptr);
    } else {
        s_onceagain = false;
    }
    return winid;
}


/*******************************************************************
** ???:      YX_LC_SecondAck
** ????:    ???????????????
** ??:       null
** ??:        null
********************************************************************/
void YX_LC_SecondAck(void)
{
    INT16U winid;
    INT8U memlen = 10;
    STREAM_T wstrm;

    if (s_onceagain == true) {                           /* ?????????? */
        YX_InitStrm(&wstrm, s_ackstream, memlen);
        winid = YX_WT_ReqSendMode1(UP_ACK_CAN_LC, s_channeltype, WT_PRIO_HIGH, s_ackstream, 10, Callback_SendResult);
        #if DEBUG_LOCK >  0
        debug_printf("?????\r\n");
        #endif
        if (winid == 0) {
            YX_MemFree(s_ackstream);
        } 
        s_onceagain = false;
    }        
}

