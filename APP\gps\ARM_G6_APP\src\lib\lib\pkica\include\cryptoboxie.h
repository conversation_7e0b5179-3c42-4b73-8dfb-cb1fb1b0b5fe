#ifndef _CRYPTOBOXIE_HEADER_H_
#define _CRYPTOBOXIE_HEADER_H_

#include "iotsecusrv_def.h"

#ifdef __cplusplus
	extern "C" {
#endif

	/* get iotSecuSrv version
	 * param @ver
	 *
	 * return 0- success  other-failed
	 */
int szitrus_getVersion(
	char** ver,
	int* vlen
);

/* synchronized devices info to iotSecuSrv
 * param @dev
 *
 * return 0- success  other-failed
 */
int szitrus_sync_device(
	pDEVICE dev
);

/* synchronized net info to iotSecuSrv
 * param @dst
 *
 * return 0- success  other-failed
 */
int szitrus_sync_domain(
	pDST dst
);

/* synchronized certificate info to iotSecuSrv
 * param @pfx
 *
 * return 0- success  other-failed
 */
int szitrus_sync_cert(
	pPFX pfx
);

/* synchronized policy info to iotSecuSrv
 * param @pol
 *
 * return 0- success  other-failed
 */
int szitrus_sync_policy(
	pPOLICY pol
);

/* synchronized policy info to iotSecuSrv
 * param @pol
 *
 * return 0- success  other-failed
 */
int szitrus_sync(
	pPARAM param
);

/* get signature with private cert
 * param @type
 * param @buf
 * param @len
 * param @datatype
 * param @pbSign
 * param @cbSign
 *
 * return 0- success  other-failed
 */
int szitrus_signature(
	const char* type,
	const char* buf,
	const int len,
	const char* datatype,
	char** pbSign,
	int* cbSign
);

/* verify signature
 * param @type
 * param @sign
 * param @signlen
 * param @src
 * param @srclen
 * param @datatype
 * param @encoded
 * param @encodedlen
 *
 * return 0- success  other-failed
 */
int szitrus_verify(
	const char* type,
	const char* sign,
	const int signlen,
	char** src,
	int* srclen,
	const char* datatype,
	char** encoded,
	int* encodedlen
);

/* use encoded to encode msg
 * param @buf
 * param @len
 * param @encoded
 * param @encodedlen
 * param @pbEncoded
 * param @cbEncoded
 *
 * return 0- success  other-failed
 */
int szitrus_encrypt(
	const char* buf,
	const int len,
	const char* datatype,
	const char* encoded,
	const int encodedlen,
	char** pbEncoded,
	int * cbEncoded
);

/* use private certificate decode msg
 * param @buf
 * param @len
 * param @pbDecoded
 * param @cbDecoded
 *
 * return 0- success  other-failed
 */
int szitrus_decrypt(
	const char* buf,
	const int len,
	const char* datatype,
	char** pbDecoded,
	int *cbDecoded
);

/* get password
 * param @pre
 * param @len
 * param @buf
 * param @plen
 *
 * return 0- success  other-failed
 */
int szitrus_certPass(
	const char* pre,
	const int len,
	char** buf,
	int* plen
);

/* cert check force
 *
 * return 0- success  other-failed
 */
int szitrus_did();

/* free buf
 *
 * return 0- success  other-failed
 */
int szitrus_free(
	void* buf
);


/* cert check status
 *
 * return 0- success  other-failed
 */
int szitrus_certStatus();

/* cert parse
*
* param index get cert index
* param buf cert info 
* param len cert info len
* return 0- success  other-failed
*/
int szitrus_certParse(char** buf, int* len);

#ifdef __cplusplus
	}
#endif

#endif
