#include "yx_includes.h"
#include "yx_debug.h"
#include "yx_dm.h"
#include "yx_debug.h"
#include "yx_base64.h"
#include "yx_obs_sign.h"
#include "yx_obs_util.h"
#include "yx_obs_sha.h"

#define CH_NEW_LINE     "\n"
#define KEY_LIST_LEN    30
#define ARRAY_LEN(a)   (sizeof(a)/sizeof(a[0]))

char const key_list[][KEY_LIST_LEN] =
{
    "CDNNotifyConfiguration", "acl", "append", "attname", "backtosource", "cors", "customdomain", "delete", "deletebucket", "directcoldaccess",
    "encryption", "inventory", "length", "lifecycle", "location", "logging", "metadata", "modify", "name", "notification", "orchestration", "partNumber", "policy",
    "position", "quota", "rename", "replication", "requestPayment", "response-cache-control", "response-content-disposition", "response-content-encoding",
    "response-content-language", "response-content-type", "response-expires", "restore", "select", "storageClass", "storagePolicy", "storageinfo", "tagging",
    "torrent", "truncate", "uploadId", "uploads", "versionId", "versioning", "versions", "website", "x-image-process", "x-image-save-bucket", "x-image-save-object",
    "x-obs-security-token",
};

static int compare(void const *kv1, void const *kv2)
{
    KV *k1 = (KV*) kv1;
    KV *k2 = (KV*) kv2;
    return strcmp(k1->k, k2->k);
}

static int in_param_key_list(char const *key)
{
    int i;
    for(i=0;i<ARRAY_LEN(key_list);i++){
        if(strcmp(key, key_list[i])==0){
            return 1;
        }
    }
    return 0;
}

static void ltoa(long value, char *string, int radix)
{
    long v, i;
    char tmp[33] = {0};
    char *tp = tmp;

    if (string == NULL) {
    	#if DEBUG_POTA > 0
        debug_printf_ex(LV_ERROR, "%s, parameter error\r\n", __func__);
        #endif
        return;
    }
    if (radix > 36 || radix <= 1)
    {
        #if DEBUG_POTA > 0
        debug_printf_ex(LV_ERROR, "%s, radix error\r\n", __func__);
        #endif
        return;
    }
    v = value;
    while (v) {
        i = v % radix;
        v = v / radix;
        if (i < 10) {
            *tp++ = i + '0';
        } else {
            *tp++ = i + 'a' - 10;
        }
    }
    while (tp > tmp) {
        *string++ = *--tp;
    }
}

int get_string_to_sign(char const *method, 
    KV const *headers, KV const *params,
    char const *bucket, char const *object, long expires, char **output)
{
    int size;
    char *contentMd5 = NULL;
    char *contentType = NULL;
    char dateOrExpire[OBS_MAX_DATE] = {0};
    KV headersKvs[OBS_HEADERS_NUM] = {0};
    KV paramsKvs[OBS_PARAMS_NUM] = {0};
    int headersCount = 0;
    int paramsCount = 0;
    int i;
    char *head;
    char *temp;
    
    size = strlen(method) + strlen(CH_NEW_LINE);
    
    if(expires > 0){
        ltoa(expires, dateOrExpire, 10);
        size += strlen(dateOrExpire) + strlen(CH_NEW_LINE);
    }

    /* deal with headers */
    if (headers != NULL) {
        for (i=0; i<OBS_HEADERS_NUM; i++) {
            KV kv = headers[i];
            string_to_lower(kv.k);
            trim_string(kv.k);
            trim_string(kv.v);
            if (strcmp(kv.k, "content-md5") == 0) {
                contentMd5 = kv.v;
                size += strlen(contentMd5) + strlen(CH_NEW_LINE);
            } else if (strcmp(kv.k, "content-type") == 0) {
                contentType = kv.v;
                size += strlen(contentType) + strlen(CH_NEW_LINE);
            } else if (strcmp(kv.k, "date") == 0) {
                strcpy(dateOrExpire, kv.v);
                size += strlen(dateOrExpire) + strlen(CH_NEW_LINE);
            } else if (strstr(kv.k, "x-obs-") == kv.k) {
                headersKvs[headersCount++] = kv;
            }
        }
        if(headersCount > 0){
            /* do sort headers by key */
            qsort(headersKvs, headersCount, sizeof(KV), compare);
        }
        
        for (i = 0; i < headersCount; i++) {
            size += strlen(headersKvs[i].k) + 1 + strlen(headersKvs[i].v) + strlen(CH_NEW_LINE);
        }
    }

    /* deal with params; */
    if(params != NULL){
        for (i =0; i<OBS_PARAMS_NUM; i++) {
            KV kv = params[i];
            if(in_param_key_list(kv.k)){
                paramsKvs[paramsCount++] = kv;
            }
        }
        
        if(paramsCount > 0){
            qsort(paramsKvs, paramsCount, sizeof(KV), compare);
            /* made this for '?' */
            size += 1;
        }
        
        for (i = 0; i < paramsCount; i++) {
            size += strlen(paramsKvs[i].k);
            if(strlen(paramsKvs[i].v) > 0){
                size += 1 + strlen(paramsKvs[i].v);
            }
        
            if(i != paramsCount - 1){
                /* made this for '&' */
                size += 1;
            }
        }
    }

    size += 1;
    if(bucket != NULL){
        size += strlen(bucket) + 1;
        if(object != NULL){
            size += strlen(object);
        }
    }

    head = (char*) YX_MemMalloc(size + 1);

    temp = head;
    *temp = '\0';

    temp = strcat(temp, method);
    temp = strcat(temp, CH_NEW_LINE);

    if (contentMd5 != NULL) {
        temp = strcat(temp, contentMd5);
    }
    temp = strcat(temp, CH_NEW_LINE);

    if (contentType != NULL) {
        temp = strcat(temp, contentType);
    }
    temp = strcat(temp, CH_NEW_LINE);

    temp = strcat(temp, dateOrExpire);
    temp = strcat(temp, CH_NEW_LINE);

    /* add CanonicalizedHeaders */
    for (i = 0; i < headersCount; i++) {
        temp = strcat(temp, headersKvs[i].k);
        temp = strcat(temp, ":");
        temp = strcat(temp, headersKvs[i].v);
        temp = strcat(temp, CH_NEW_LINE);
    }

    /* add CanonicalizedResource */
    temp = strcat(temp, "/");
    if(bucket != NULL && strlen(bucket) > 0){
        temp = strcat(temp, bucket);
        temp = strcat(temp, "/");
        if(object != NULL && strlen(object) > 0){
            temp = strcat(temp, object);
        }
    }

    //add params
    for (i = 0; i < paramsCount; i++) {
        if(i == 0){
            temp = strcat(temp, "?");
        }
        temp = strcat(temp, paramsKvs[i].k);
        if(strlen(paramsKvs[i].v) > 0){
            temp = strcat(temp, "=");
            temp = strcat(temp, paramsKvs[i].v);
        }

        if(i != paramsCount - 1){
            temp = strcat(temp, "&");
        }
    }

    *output = head;
    return 0;
}

int get_signature_for_header_auth(char const *sk, char const *string_to_sign, char *output)
{
    unsigned char hmac[20] = {0};
    hmac_sha1(hmac, (unsigned char*)sk, strlen(sk), (unsigned char*)string_to_sign, strlen(string_to_sign));

    base64_encode(hmac, 20, output);
    trim_string(output);

    return 0;
}

#if 0
int get_signature_for_url_auth(char const *sk, char const *string_to_sign, char **output)
{
	unsigned char hmac[20] = {0};
	char *temp;
	
	hmac_sha1(hmac, (unsigned char*)sk, strlen(sk), (unsigned char*)string_to_sign, strlen(string_to_sign));
	temp = (char*) YX_MemMalloc(1024);
	base64_encode(hmac, 20, temp);
	trim_string(temp);

	*output = (char*) YX_MemMalloc(1024);

	url_encode(*output, temp, strlen(temp));

	free(temp);
	return 0;
}
#endif

