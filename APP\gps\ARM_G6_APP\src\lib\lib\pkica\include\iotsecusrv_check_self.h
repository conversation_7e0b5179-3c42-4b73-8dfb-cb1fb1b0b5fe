/** \ 微服务自检测接口说明和错误码说明*/

/******************************************
**\ 微服务启动判断
*
*
*\@ return 0 - success  other - failed
*
**************************************/
int szitrus_check_run(void);

/******************************************
**\ 触发微服务功能完整性自检
*
*
*\@ return 0 - success  other - failed
*
**************************************/
int szitrus_check_self_run(void);


/******************************************
**\ 功能完整性自检是否结束
*
*
*\@  return 0 - success
*    0x0001 ERROR_FAILED_CHECK //自检失败
*    0xff00 ERROR_ENV_RUN  //自检服务进行中
*
**************************************/
int szitrus_check_self_over(void);


/******************************************
**\ 查询微服务功能完整性自检结果
*
*
*\@ return 0 - success  other - failed
*
*\maybe code return include
*  0xff80 ERROR_ENV_SM2    //(SM2算法服务异常)SM2 algorithm env error
*  0xff40 ERROR_ENV_SM3    //(SM3算法服务异常)SM3 algorithm env error
*  0xff20 ERROR_ENV_SM4    //(SM4算法服务异常)SM4 algorithm env error
*  0xff10 ERROR_ENV_RSA    //(RSA算法服务异常)RSA algorithm env error
*  0xff08 ERROR_ENV_SOFT    //(软件完整性异常)soft completeness env error
*  0xff04 ERROR_ENV_RAND    //(SM2算法服务异常)rand algorithm env error
*  0xff02 ERROR_ENV_V2X   //(V2X环境异常)reserved
*  0xff01 ERROR_ENV_KEY   //(密钥对环境异常)reserved
*
* \ 返回值判断示例
*	if(ret & ERROR_ENV_SM2) SM2 error  [ret接口返回值]
*	if(ret & ERROR_ENV_RSA) RSA error  [ret接口返回值]
**************************************/
int szitrus_check_self_result(void); // 


