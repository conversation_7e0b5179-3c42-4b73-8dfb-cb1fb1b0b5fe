#ifndef _IOTSECUSRV_DEF_HEADER_H_
#define _IOTSECUSRV_DEF_HEADER_H_

#define MSG_LEN_MIX 32

typedef enum key_type_st {
	e_rsa = 0,
	e_sm2,
	e_ecc
}KEY_TYPE;

typedef enum symm_key_type_st {
	e_aes_128 = 0,
	e_aes_256,
	e_des,
	e_3des,
	e_rc4,
	e_sm4
}SYMM_KEY_TYPE;

typedef enum symm_mode_st {
	e_ecb = 0,
	e_cbc,
	e_cfb,
	e_ofb,
	e_ctr
}SYMM_MODE;

typedef enum digest_type_st {
	e_md5 = 0,
	e_sha1,
	e_sha256,
	e_sha512,
	e_sm3
}DIGEST_TYPE;

typedef enum cert_identify_st {
	r_first = 0x01, //first to download cert or download cert location 
	r_update = 0x02,//update cert , cert status is ok or not ok . force to update
	r_break = 0x04, //check cert broken,
	r_revoke = 0x08 //server revoke or modify cert status
}CERT_IDENTY;

typedef struct x509_ext_st {
	const char* ext;
	const int index;
}X509_EXT,*pX509_EXT;

typedef struct pfx_st {
	const char* location;
	const char* chipLocation;
	const char* prepassword;
	const char* keyLocation;  //SM2 soft certificate key location

	pX509_EXT  ext;
	int ext_size;

	const CERT_IDENTY identify;
}PFX, *pPFX;

typedef struct device_st {
	const char* vin;
	const char* sn;
	const char* imei;//2reserved
	const char* uin;  //2reserved
	const char* reserved;
}DEVICE, *pDEVICE;

typedef struct certpolicy_st {
	int cycleDay;
	int cycleHour;
	int cycleMin;
	int did;
	int off;
	const char* reserved;
}POLICY, *pPOLICY;

typedef struct dst_st {
	const char* domain;
	int port;
}DST, *pDST;

typedef struct param_st {
	pPFX pfx;
	pDEVICE dev;
	pPOLICY plc;
	pDST dst;
	int certType;
	const char* type;
}PARAM, *pPARAM;

#define PKCS_1_PADDING "PKCS1#Padding"
#define PKCS_1_NO_PADDING "PKCS1#Nopadding"
#define PKCS_1_PSS_RSA "PKCS1#PSS"
#define PKCS_7_ATTACH "PKCS7#Attach"
#define PKCS_7_DETATCH "PKCS7#Detach"
#define PKCS_7_PSS_RSA "PKCS7#PSS"

#define DATA_TYPE_BASE64 "base64"
#define DATA_TYPE_HEX "hex"
#define DATA_TYPE_TEXT "text"

#endif